{"build": "py38haa95532_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main", "constrains": [], "depends": ["python >=3.8,<3.9.0a0"], "extracted_package_dir": "C:\\ProgramData\\miniconda3\\pkgs\\wheel-0.44.0-py38haa95532_0", "files": ["Lib/site-packages/wheel-0.44.0.dist-info/LICENSE.txt", "Lib/site-packages/wheel-0.44.0.dist-info/METADATA", "Lib/site-packages/wheel-0.44.0.dist-info/RECORD", "Lib/site-packages/wheel-0.44.0.dist-info/WHEEL", "Lib/site-packages/wheel-0.44.0.dist-info/entry_points.txt", "Lib/site-packages/wheel/__init__.py", "Lib/site-packages/wheel/__main__.py", "Lib/site-packages/wheel/__pycache__/__init__.cpython-38.pyc", "Lib/site-packages/wheel/__pycache__/__main__.cpython-38.pyc", "Lib/site-packages/wheel/__pycache__/_bdist_wheel.cpython-38.pyc", "Lib/site-packages/wheel/__pycache__/_setuptools_logging.cpython-38.pyc", "Lib/site-packages/wheel/__pycache__/bdist_wheel.cpython-38.pyc", "Lib/site-packages/wheel/__pycache__/macosx_libfile.cpython-38.pyc", "Lib/site-packages/wheel/__pycache__/metadata.cpython-38.pyc", "Lib/site-packages/wheel/__pycache__/util.cpython-38.pyc", "Lib/site-packages/wheel/__pycache__/wheelfile.cpython-38.pyc", "Lib/site-packages/wheel/_bdist_wheel.py", "Lib/site-packages/wheel/_setuptools_logging.py", "Lib/site-packages/wheel/bdist_wheel.py", "Lib/site-packages/wheel/cli/__init__.py", "Lib/site-packages/wheel/cli/__pycache__/__init__.cpython-38.pyc", "Lib/site-packages/wheel/cli/__pycache__/convert.cpython-38.pyc", "Lib/site-packages/wheel/cli/__pycache__/pack.cpython-38.pyc", "Lib/site-packages/wheel/cli/__pycache__/tags.cpython-38.pyc", "Lib/site-packages/wheel/cli/__pycache__/unpack.cpython-38.pyc", "Lib/site-packages/wheel/cli/convert.py", "Lib/site-packages/wheel/cli/pack.py", "Lib/site-packages/wheel/cli/tags.py", "Lib/site-packages/wheel/cli/unpack.py", "Lib/site-packages/wheel/macosx_libfile.py", "Lib/site-packages/wheel/metadata.py", "Lib/site-packages/wheel/util.py", "Lib/site-packages/wheel/vendored/__init__.py", "Lib/site-packages/wheel/vendored/__pycache__/__init__.cpython-38.pyc", "Lib/site-packages/wheel/vendored/packaging/__init__.py", "Lib/site-packages/wheel/vendored/packaging/__pycache__/__init__.cpython-38.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_elffile.cpython-38.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_manylinux.cpython-38.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_musllinux.cpython-38.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_parser.cpython-38.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_structures.cpython-38.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-38.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/markers.cpython-38.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/requirements.cpython-38.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/specifiers.cpython-38.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/tags.cpython-38.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/utils.cpython-38.pyc", "Lib/site-packages/wheel/vendored/packaging/__pycache__/version.cpython-38.pyc", "Lib/site-packages/wheel/vendored/packaging/_elffile.py", "Lib/site-packages/wheel/vendored/packaging/_manylinux.py", "Lib/site-packages/wheel/vendored/packaging/_musllinux.py", "Lib/site-packages/wheel/vendored/packaging/_parser.py", "Lib/site-packages/wheel/vendored/packaging/_structures.py", "Lib/site-packages/wheel/vendored/packaging/_tokenizer.py", "Lib/site-packages/wheel/vendored/packaging/markers.py", "Lib/site-packages/wheel/vendored/packaging/requirements.py", "Lib/site-packages/wheel/vendored/packaging/specifiers.py", "Lib/site-packages/wheel/vendored/packaging/tags.py", "Lib/site-packages/wheel/vendored/packaging/utils.py", "Lib/site-packages/wheel/vendored/packaging/version.py", "Lib/site-packages/wheel/vendored/vendor.txt", "Lib/site-packages/wheel/wheelfile.py", "Scripts/wheel-script.py", "Scripts/wheel.exe"], "fn": "wheel-0.44.0-py38haa95532_0.conda", "license": "MIT", "link": {"source": "C:\\ProgramData\\miniconda3\\pkgs\\wheel-0.44.0-py38haa95532_0", "type": 1}, "md5": "cd0fce89894afe4c243b75e1857400a1", "name": "wheel", "package_tarball_full_path": "C:\\ProgramData\\miniconda3\\pkgs\\wheel-0.44.0-py38haa95532_0.conda", "paths_data": {"paths": [{"_path": "Lib/site-packages/wheel-0.44.0.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "sha256_in_prefix": "30c23618679108f3e8ea1d2a658c7ca417bdfc891c98ef1a89fa4ff0c9828654", "size_in_bytes": 1107}, {"_path": "Lib/site-packages/wheel-0.44.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "3a3c69baae37bab03a835fa8b8a3128f08d69fb513345812beab7c6e5afee041", "sha256_in_prefix": "3a3c69baae37bab03a835fa8b8a3128f08d69fb513345812beab7c6e5afee041", "size_in_bytes": 2313}, {"_path": "Lib/site-packages/wheel-0.44.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "0a2462d7a8cbb43e23610578db119d3be9ef04a9ff305dbae57d41b626afc461", "sha256_in_prefix": "0a2462d7a8cbb43e23610578db119d3be9ef04a9ff305dbae57d41b626afc461", "size_in_bytes": 2908}, {"_path": "Lib/site-packages/wheel-0.44.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "sha256_in_prefix": "1196c6921ec87b83e865f450f08d19b8ff5592537f4ef719e83484e546abe33e", "size_in_bytes": 81}, {"_path": "Lib/site-packages/wheel-0.44.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "sha256_in_prefix": "ad363505b90f1e1906326e10dc5d29233241cd6da4331a06d68ae27dfbc6740d", "size_in_bytes": 104}, {"_path": "Lib/site-packages/wheel/__init__.py", "path_type": "hardlink", "sha256": "1b474a6c75845852460e464822eda21682713f4a68534da542077a524a82f9a0", "sha256_in_prefix": "1b474a6c75845852460e464822eda21682713f4a68534da542077a524a82f9a0", "size_in_bytes": 59}, {"_path": "Lib/site-packages/wheel/__main__.py", "path_type": "hardlink", "sha256": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "sha256_in_prefix": "3643149ee4c219c3a4818d0804b8010950bf04619c58e471d8af236064b5d941", "size_in_bytes": 455}, {"_path": "Lib/site-packages/wheel/__pycache__/__init__.cpython-38.pyc", "path_type": "hardlink", "sha256": "8e9dd9795ede406e0e5e35ce7a3a4da46f10ac1d467373a4bde81961b97545d2", "sha256_in_prefix": "8e9dd9795ede406e0e5e35ce7a3a4da46f10ac1d467373a4bde81961b97545d2", "size_in_bytes": 199}, {"_path": "Lib/site-packages/wheel/__pycache__/__main__.cpython-38.pyc", "path_type": "hardlink", "sha256": "6ba688accfb7b9a0cda7448a0062b9838c1efd84ac37455bde429ab16cd00183", "sha256_in_prefix": "6ba688accfb7b9a0cda7448a0062b9838c1efd84ac37455bde429ab16cd00183", "size_in_bytes": 591}, {"_path": "Lib/site-packages/wheel/__pycache__/_bdist_wheel.cpython-38.pyc", "path_type": "hardlink", "sha256": "7c25e810fe378480ae35841e8e228743d82eb35836b08e24c598536b9cc914a3", "sha256_in_prefix": "7c25e810fe378480ae35841e8e228743d82eb35836b08e24c598536b9cc914a3", "size_in_bytes": 14783}, {"_path": "Lib/site-packages/wheel/__pycache__/_setuptools_logging.cpython-38.pyc", "path_type": "hardlink", "sha256": "2c48272fb14df75a92112aa797241c8201027a0436dd26025fbaa8e4b172d57d", "sha256_in_prefix": "2c48272fb14df75a92112aa797241c8201027a0436dd26025fbaa8e4b172d57d", "size_in_bytes": 976}, {"_path": "Lib/site-packages/wheel/__pycache__/bdist_wheel.cpython-38.pyc", "path_type": "hardlink", "sha256": "d28cdc00257c567853134f7f4d902d542518ea56215e0c2248b869390276a493", "sha256_in_prefix": "d28cdc00257c567853134f7f4d902d542518ea56215e0c2248b869390276a493", "size_in_bytes": 502}, {"_path": "Lib/site-packages/wheel/__pycache__/macosx_libfile.cpython-38.pyc", "path_type": "hardlink", "sha256": "125d957f6c6907ba41495252e2425260bbca797cb544e595ddc764c5b68c5c49", "sha256_in_prefix": "125d957f6c6907ba41495252e2425260bbca797cb544e595ddc764c5b68c5c49", "size_in_bytes": 10464}, {"_path": "Lib/site-packages/wheel/__pycache__/metadata.cpython-38.pyc", "path_type": "hardlink", "sha256": "e918b0fdc4fba464e14959f33451ce8fece97052b082c4d4f9c220573499b496", "sha256_in_prefix": "e918b0fdc4fba464e14959f33451ce8fece97052b082c4d4f9c220573499b496", "size_in_bytes": 5997}, {"_path": "Lib/site-packages/wheel/__pycache__/util.cpython-38.pyc", "path_type": "hardlink", "sha256": "f22281fb0feca77a5c1b9d3a0137b1623a33c214c5736ab2e7db35255be0e42b", "sha256_in_prefix": "f22281fb0feca77a5c1b9d3a0137b1623a33c214c5736ab2e7db35255be0e42b", "size_in_bytes": 827}, {"_path": "Lib/site-packages/wheel/__pycache__/wheelfile.cpython-38.pyc", "path_type": "hardlink", "sha256": "9547ddb996620adfeb4492a2042f2ada534790392f10a3d81dc0c8c7beace361", "sha256_in_prefix": "9547ddb996620adfeb4492a2042f2ada534790392f10a3d81dc0c8c7beace361", "size_in_bytes": 6291}, {"_path": "Lib/site-packages/wheel/_bdist_wheel.py", "path_type": "hardlink", "sha256": "d32e284a36db98350c0e256b84d8cd64e394ed34fce5839b32248aed3f916126", "sha256_in_prefix": "d32e284a36db98350c0e256b84d8cd64e394ed34fce5839b32248aed3f916126", "size_in_bytes": 21496}, {"_path": "Lib/site-packages/wheel/_setuptools_logging.py", "path_type": "hardlink", "sha256": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "sha256_in_prefix": "fb9282fa59ded2294e5162037ce92a6a951618c15986e2980c86af219881e643", "size_in_bytes": 781}, {"_path": "Lib/site-packages/wheel/bdist_wheel.py", "path_type": "hardlink", "sha256": "f9b5159a79fb4468dc19b8ef9f94284331514e23adf994c5cb86ec23582b0b85", "sha256_in_prefix": "f9b5159a79fb4468dc19b8ef9f94284331514e23adf994c5cb86ec23582b0b85", "size_in_bytes": 376}, {"_path": "Lib/site-packages/wheel/cli/__init__.py", "path_type": "hardlink", "sha256": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "sha256_in_prefix": "369abafe32a2d3776121c46799bb85870be2549c703b4b5812712158cbfd709a", "size_in_bytes": 4402}, {"_path": "Lib/site-packages/wheel/cli/__pycache__/__init__.cpython-38.pyc", "path_type": "hardlink", "sha256": "74788100f6afca5158a47ba7e2623672ff13a190e85bfcd77dde03abb54c8369", "sha256_in_prefix": "74788100f6afca5158a47ba7e2623672ff13a190e85bfcd77dde03abb54c8369", "size_in_bytes": 4596}, {"_path": "Lib/site-packages/wheel/cli/__pycache__/convert.cpython-38.pyc", "path_type": "hardlink", "sha256": "f7deccb15bed11e6f20ef4c47202e39c48012baa8d99db750f6d19cd6386205e", "sha256_in_prefix": "f7deccb15bed11e6f20ef4c47202e39c48012baa8d99db750f6d19cd6386205e", "size_in_bytes": 6323}, {"_path": "Lib/site-packages/wheel/cli/__pycache__/pack.cpython-38.pyc", "path_type": "hardlink", "sha256": "ee42b8465fc8e2d44b8b2ed42de0d55faa5746457925c2352131e6d303a096f4", "sha256_in_prefix": "ee42b8465fc8e2d44b8b2ed42de0d55faa5746457925c2352131e6d303a096f4", "size_in_bytes": 3020}, {"_path": "Lib/site-packages/wheel/cli/__pycache__/tags.cpython-38.pyc", "path_type": "hardlink", "sha256": "4274ace608f3a71e5f1ca44656bbb4c6c9a724807ad43a7b431449534b5c525f", "sha256_in_prefix": "4274ace608f3a71e5f1ca44656bbb4c6c9a724807ad43a7b431449534b5c525f", "size_in_bytes": 3733}, {"_path": "Lib/site-packages/wheel/cli/__pycache__/unpack.cpython-38.pyc", "path_type": "hardlink", "sha256": "29316b3912305f6b65f7d0d435a5bb351a4bd2ca4648c0652eb82bb8c37f0504", "sha256_in_prefix": "29316b3912305f6b65f7d0d435a5bb351a4bd2ca4648c0652eb82bb8c37f0504", "size_in_bytes": 1023}, {"_path": "Lib/site-packages/wheel/cli/convert.py", "path_type": "hardlink", "sha256": "c4d9f28a31ebc4dd3acddb5e41f338850d9f48fa20980c0226c0281c8075f412", "sha256_in_prefix": "c4d9f28a31ebc4dd3acddb5e41f338850d9f48fa20980c0226c0281c8075f412", "size_in_bytes": 9512}, {"_path": "Lib/site-packages/wheel/cli/pack.py", "path_type": "hardlink", "sha256": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "sha256_in_prefix": "08015c1dd055ba5bec1d82659dd2953bb28c23d26a053673e628b43cac7108eb", "size_in_bytes": 3103}, {"_path": "Lib/site-packages/wheel/cli/tags.py", "path_type": "hardlink", "sha256": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "sha256_in_prefix": "947c3e2da5ab912e49cbfa96730fbaa528de34ceb20230e7a8a2371392534c25", "size_in_bytes": 4760}, {"_path": "Lib/site-packages/wheel/cli/unpack.py", "path_type": "hardlink", "sha256": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "sha256_in_prefix": "63f27bca7c4f4a81454d3ec7d1f3206c195512bc320c670e6e099ee4c06ecf9f", "size_in_bytes": 1021}, {"_path": "Lib/site-packages/wheel/macosx_libfile.py", "path_type": "hardlink", "sha256": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "sha256_in_prefix": "935c7b084dcb3ed3951aa8fa3574359d319854f69e46b855cd41bf28fab7cc3b", "size_in_bytes": 16572}, {"_path": "Lib/site-packages/wheel/metadata.py", "path_type": "hardlink", "sha256": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "sha256_in_prefix": "242e29ee395066ed9b513010d9f7af92a2e383f5fa8273724612e7e8e50ed6d7", "size_in_bytes": 6171}, {"_path": "Lib/site-packages/wheel/util.py", "path_type": "hardlink", "sha256": "7b48e99ec6db33d42169a312c9aa7efd9814c5cc70a722c393a44772b76e3cb8", "sha256_in_prefix": "7b48e99ec6db33d42169a312c9aa7efd9814c5cc70a722c393a44772b76e3cb8", "size_in_bytes": 621}, {"_path": "Lib/site-packages/wheel/vendored/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/wheel/vendored/__pycache__/__init__.cpython-38.pyc", "path_type": "hardlink", "sha256": "02caceea521163ea5c957a8d79648ba469de110d51261ea12a3b0a76b22f7855", "sha256_in_prefix": "02caceea521163ea5c957a8d79648ba469de110d51261ea12a3b0a76b22f7855", "size_in_bytes": 135}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/__init__.cpython-38.pyc", "path_type": "hardlink", "sha256": "099609bc36a9a62fe759852fbb1b32bccd5585f016527f5e5a720dc61155e5a3", "sha256_in_prefix": "099609bc36a9a62fe759852fbb1b32bccd5585f016527f5e5a720dc61155e5a3", "size_in_bytes": 145}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_elffile.cpython-38.pyc", "path_type": "hardlink", "sha256": "5ad503fa804463a6edcfe09080d8c1f520549533c0d5e67b0365e8522de3ad03", "sha256_in_prefix": "5ad503fa804463a6edcfe09080d8c1f520549533c0d5e67b0365e8522de3ad03", "size_in_bytes": 3315}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_manylinux.cpython-38.pyc", "path_type": "hardlink", "sha256": "70f8e3c5bdf8111565c2c784487489197f9af9d531d552cd8b76ad07d9a5b7c9", "sha256_in_prefix": "70f8e3c5bdf8111565c2c784487489197f9af9d531d552cd8b76ad07d9a5b7c9", "size_in_bytes": 6351}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_musllinux.cpython-38.pyc", "path_type": "hardlink", "sha256": "1c2d5ea4ee99433aff4d12d9bc0b9a57876ab9f6e79bf74874bfdb06d3ce0cc8", "sha256_in_prefix": "1c2d5ea4ee99433aff4d12d9bc0b9a57876ab9f6e79bf74874bfdb06d3ce0cc8", "size_in_bytes": 3285}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_parser.cpython-38.pyc", "path_type": "hardlink", "sha256": "534ea1077ca476ee6492c0dae8cb2494f6fb8ba23d12f30c1791ff66177dfde7", "sha256_in_prefix": "534ea1077ca476ee6492c0dae8cb2494f6fb8ba23d12f30c1791ff66177dfde7", "size_in_bytes": 8893}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_structures.cpython-38.pyc", "path_type": "hardlink", "sha256": "c33e02c550931996e6804987a2fe914d4ef5dd6c150125ed4184d3c922b5c6c4", "sha256_in_prefix": "c33e02c550931996e6804987a2fe914d4ef5dd6c150125ed4184d3c922b5c6c4", "size_in_bytes": 2755}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/_tokenizer.cpython-38.pyc", "path_type": "hardlink", "sha256": "738f571a7fb87d124a445f05e54cdb6bdc7f90e3ec872303688ff93dedcf76bb", "sha256_in_prefix": "738f571a7fb87d124a445f05e54cdb6bdc7f90e3ec872303688ff93dedcf76bb", "size_in_bytes": 5683}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/markers.cpython-38.pyc", "path_type": "hardlink", "sha256": "25dac2ab252f601e9dd96c6c2e6b868944ebcde1899fd2deedfa800941dcf300", "sha256_in_prefix": "25dac2ab252f601e9dd96c6c2e6b868944ebcde1899fd2deedfa800941dcf300", "size_in_bytes": 6972}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/requirements.cpython-38.pyc", "path_type": "hardlink", "sha256": "4c14482885c089dc9e35a85b569d5d9a21dfab597d4b7f607d08886d112788a8", "sha256_in_prefix": "4c14482885c089dc9e35a85b569d5d9a21dfab597d4b7f607d08886d112788a8", "size_in_bytes": 2791}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/specifiers.cpython-38.pyc", "path_type": "hardlink", "sha256": "f8c63f01a5368353f8ef707a2882b8ca0e52e89af75a86ba761cc1128e6f4fed", "sha256_in_prefix": "f8c63f01a5368353f8ef707a2882b8ca0e52e89af75a86ba761cc1128e6f4fed", "size_in_bytes": 31032}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/tags.cpython-38.pyc", "path_type": "hardlink", "sha256": "e54db183e4fe2779cf2367202d291df071dac63ab8cc6a7238a453b0b1cffe69", "sha256_in_prefix": "e54db183e4fe2779cf2367202d291df071dac63ab8cc6a7238a453b0b1cffe69", "size_in_bytes": 13840}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/utils.cpython-38.pyc", "path_type": "hardlink", "sha256": "b8e5ff3aecf1a35ef7c339fec9b23edde406773eba7c2c70c02f283d0a6636c9", "sha256_in_prefix": "b8e5ff3aecf1a35ef7c339fec9b23edde406773eba7c2c70c02f283d0a6636c9", "size_in_bytes": 4559}, {"_path": "Lib/site-packages/wheel/vendored/packaging/__pycache__/version.cpython-38.pyc", "path_type": "hardlink", "sha256": "153e1644f1ab1301db85bf6ef441736469abdbe79c455e05623ef678941c7049", "sha256_in_prefix": "153e1644f1ab1301db85bf6ef441736469abdbe79c455e05623ef678941c7049", "size_in_bytes": 14286}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_elffile.py", "path_type": "hardlink", "sha256": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "sha256_in_prefix": "85b98af0e0fa67b7d8ea1c229c7114703d5bcbb73390688d62eed28671449369", "size_in_bytes": 3266}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_manylinux.py", "path_type": "hardlink", "sha256": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "sha256_in_prefix": "3fbb1d479ffb5c1634f4b55860f8479b274c2482303d75ac878a2593be14ba3e", "size_in_bytes": 9588}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_musllinux.py", "path_type": "hardlink", "sha256": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "sha256_in_prefix": "cf5b3c4e8da1434be99ff77e3b68b9ab11b010af1698694bb7777fdba57b35e6", "size_in_bytes": 2674}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_parser.py", "path_type": "hardlink", "sha256": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "sha256_in_prefix": "e2d4f87a64a5daa4da53b553404d576bda358cc3c2b017b3b18071c8d31437eb", "size_in_bytes": 10347}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_structures.py", "path_type": "hardlink", "sha256": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "sha256_in_prefix": "ab77953666d62461bf4b40e2b7f4b7028f2a42acffe4f6135c500a0597b9cabe", "size_in_bytes": 1431}, {"_path": "Lib/site-packages/wheel/vendored/packaging/_tokenizer.py", "path_type": "hardlink", "sha256": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "sha256_in_prefix": "6a50ad6f05e138502614667a050fb0093485a11009db3fb2b087fbfff31327f9", "size_in_bytes": 5292}, {"_path": "Lib/site-packages/wheel/vendored/packaging/markers.py", "path_type": "hardlink", "sha256": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "sha256_in_prefix": "fd348f2350612583bb069f40cd398743122a1c45576938e60e1f46fb0f2accf0", "size_in_bytes": 8232}, {"_path": "Lib/site-packages/wheel/vendored/packaging/requirements.py", "path_type": "hardlink", "sha256": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "sha256_in_prefix": "760a01795a6b3eed9813a43c9c67f038f4e30131db45afd918bc978451259fa4", "size_in_bytes": 2933}, {"_path": "Lib/site-packages/wheel/vendored/packaging/specifiers.py", "path_type": "hardlink", "sha256": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "sha256_in_prefix": "2164add12acb48fef685e5a1002f142f4786bdab3b5c84078ea8958957e63ca1", "size_in_bytes": 39778}, {"_path": "Lib/site-packages/wheel/vendored/packaging/tags.py", "path_type": "hardlink", "sha256": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "sha256_in_prefix": "7de7475e2387901c4d6535e8b57bfcb973e630553d69ef93281ba38181e281c0", "size_in_bytes": 18950}, {"_path": "Lib/site-packages/wheel/vendored/packaging/utils.py", "path_type": "hardlink", "sha256": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "sha256_in_prefix": "5e07663f7cb1f7ec101058ceecebcc8fd46311fe49951e4714547af6fed243d1", "size_in_bytes": 5268}, {"_path": "Lib/site-packages/wheel/vendored/packaging/version.py", "path_type": "hardlink", "sha256": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "sha256_in_prefix": "3c525a6190f1060cb191f6211f7490c38a9f13d202096ad39a2b6fab5e32ddbb", "size_in_bytes": 16234}, {"_path": "Lib/site-packages/wheel/vendored/vendor.txt", "path_type": "hardlink", "sha256": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "sha256_in_prefix": "67610d8c1d62e69adf7b3f0274cd5276bddce99c6fdab451a253292e60677001", "size_in_bytes": 16}, {"_path": "Lib/site-packages/wheel/wheelfile.py", "path_type": "hardlink", "sha256": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "sha256_in_prefix": "5120adb4d949c1a7f1b79d5860514a1bb8e7c73f1d7e16f2a8064bea331303db", "size_in_bytes": 8411}, {"_path": "Scripts/wheel-script.py", "path_type": "hardlink", "sha256": "f81e86d226fd97f3a9fb708b803859f8922740e5b62a4577b0b6b8b1d6b8e333", "sha256_in_prefix": "f81e86d226fd97f3a9fb708b803859f8922740e5b62a4577b0b6b8b1d6b8e333", "size_in_bytes": 203}, {"_path": "Scripts/wheel.exe", "path_type": "hardlink", "sha256": "f24d102084620e54fe68d07f6e9169118b283f8d33d0c8f4b974c2e05a306059", "sha256_in_prefix": "f24d102084620e54fe68d07f6e9169118b283f8d33d0c8f4b974c2e05a306059", "size_in_bytes": 54032}], "paths_version": 1}, "requested_spec": "None", "sha256": "0228cad684cb4aff0e367cbbf5749daac4989a6c7f1f515578634219931543b7", "size": 140124, "subdir": "win-64", "timestamp": 1726165065000, "url": "https://repo.anaconda.com/pkgs/main/win-64/wheel-0.44.0-py38haa95532_0.conda", "version": "0.44.0"}