{"vulns_active": [{"comment": "A really scary exploit", "id": "ANACONDA-2020-0001", "patch_fn": "patches/18284.patch", "patch_src_url": "https://github.com/python/cpython/abc1234567890/blobs/patches/18284.patch", "pkg_build_string_match": "python", "poc_url": "https://some.url", "references": [{"type": "nist", "url": "https://nvd.nist.gov/vuln/detail/CVE-2020-8492"}, {"type": "debian", "url": "https://security-tracker.debian.org/tracker/CVE-2020-8492"}]}], "vulns_mitigated": [{"comment": "Backport from master, scheduled for upstream 3.9 release", "id": "CVE-2020-8492", "patch_fn": "patches/18284.patch", "patch_src_url": "https://github.com/python/cpython/abc1234567890/blobs/patches/18284.patch", "pkg_build_string_match": "python", "poc_url": "https://some.url", "references": [{"type": "nist", "url": "https://nvd.nist.gov/vuln/detail/CVE-2020-849"}, {"type": "debian", "url": "https://security-tracker.debian.org/tracker/CVE-2020-8492"}]}, {"comment": "A really scary exploit", "id": "ANACONDA-2020-0001", "patch_fn": "patches/18284.patch", "patch_src_url": "https://github.com/python/cpython/abc1234567890/blobs/patches/18284.patch", "poc_url": "https://some.url", "references": [{"type": "nist", "url": "https://nvd.nist.gov/vuln/detail/CVE-2020-8492"}, {"type": "debian", "url": "https://security-tracker.debian.org/tracker/CVE-2020-8492"}]}]}