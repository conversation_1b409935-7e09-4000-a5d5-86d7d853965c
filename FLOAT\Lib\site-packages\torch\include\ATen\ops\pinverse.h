#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/pinverse_ops.h>

namespace at {


// aten::pinverse(Tensor self, float rcond=1e-15) -> Tensor
inline at::Tensor pinverse(const at::Tensor & self, double rcond=1e-15) {
    return at::_ops::pinverse::call(self, rcond);
}

}
