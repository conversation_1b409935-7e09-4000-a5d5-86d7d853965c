Metadata-Version: 2.1
Name: albucore
Version: 0.0.16
Summary: A high-performance image processing library designed to optimize and extend the Albumentations library with specialized functions for advanced image transformations. Perfect for developers working in computer vision who require efficient and scalable image augmentation.
Home-page: https://github.com/albumentations-team/albucore
Author: Vladimir <PERSON>lovikov
License: MIT
Keywords: Image Processing,Computer Vision,Image Augmentation,Albumentations,Optimization,Machine Learning,Deep Learning,Python Imaging,Data Augmentation,Performance,Efficiency,High-Performance,CV,OpenCV,Automation
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Classifier: Topic :: Scientific/Engineering :: Image Processing
Classifier: Typing :: Typed
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: numpy>=1.24
Requires-Dist: opencv-python-headless>=********
Requires-Dist: typing-extensions>=4.9.0; python_version < "3.10"

# Albucore

Albucore is a high-performance image processing library designed to optimize operations on images using Python and OpenCV, building upon the foundations laid by the popular Albumentations library. It offers specialized optimizations for different image data types and aims to provide faster processing times through efficient algorithm implementations.

## Features

- Optimized image multiplication operations for both `uint8` and `float32` data types.
- Support for single-channel and multi-channel images.
- Custom decorators to manage channel dimensions and output constraints.

## Installation

Install Albucore using pip:

```bash
pip install -U albucore
```

## Example

Here's how you can use Albucore to multiply an image by a constant or a vector:

```python
import cv2
import numpy as np
from albucore import multiply

# Load an image
img = cv2.imread('path_to_your_image.jpg')

# Multiply by a constant
multiplied_image = multiply(img, 1.5)

# Multiply by a vector
multiplier = [1.5, 1.2, 0.9]  # Different multiplier for each channel
multiplied_image = multiply(img, multiplier)
```

## Benchmarks

For detailed benchmark results, including other configurations and data types, refer to the [Benchmark](benchmark/results/) in the repository.

## License

Distributed under the MIT License. See LICENSE for more information.
